import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import classes from './JobItemSkeleton.module.scss';

export interface JobItemSkeletonProps {
  className?: string;
}

const JobItemSkeleton: React.FC<JobItemSkeletonProps> = ({ className }) => (
  <>
    <Skeleton className={cnj(classes.jobItem, className)} />
  </>
);

export default JobItemSkeleton;
