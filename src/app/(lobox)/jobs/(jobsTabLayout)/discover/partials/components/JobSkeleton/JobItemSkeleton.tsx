import React from 'react';
import cnj from 'shared/uikit/utils/cnj';

export interface JobItemSkeletonProps {
  className?: string;
}

const JobItemSkeleton: React.FC<JobItemSkeletonProps> = ({ className }) => (
  <div className={cnj(
    "bg-skeletonBg rounded-xl p-6 space-y-4 animate-pulse",
    "h-[335px] md:h-[353px] w-full",
    "mt-4 first:mt-0 md:mt-8 md:first:mt-0 md:nth-child-2:mt-0",
    "md:w-[calc(50%-8px)]",
    className
  )}>
    {/* Header section with avatar and title */}
    <div className="flex items-start gap-4">
      <div className="w-12 h-12 bg-gray_5 rounded-lg flex-shrink-0"></div>
      <div className="flex-1 space-y-2">
        <div className="h-4 bg-gray_5 rounded w-3/4"></div>
        <div className="h-3 bg-gray_5 rounded w-1/2"></div>
      </div>
    </div>

    {/* Description section */}
    <div className="space-y-2">
      <div className="h-3 bg-gray_5 rounded w-full"></div>
      <div className="h-3 bg-gray_5 rounded w-5/6"></div>
    </div>

    {/* Category/Tag */}
    <div className="h-3 bg-gray_5 rounded w-24"></div>

    {/* Skills/Tags row */}
    <div className="flex gap-3">
      <div className="h-8 bg-gray_5 rounded flex-1"></div>
      <div className="h-8 bg-gray_5 rounded flex-1"></div>
      <div className="h-8 bg-gray_5 rounded flex-1"></div>
    </div>

    {/* Bottom section with location, salary, and button */}
    <div className="flex items-center justify-between pt-2">
      <div className="flex gap-3">
        <div className="h-6 bg-gray_5 rounded w-20"></div>
        <div className="h-6 bg-gray_5 rounded w-16"></div>
      </div>
      <div className="h-8 bg-gray_5 rounded w-20"></div>
    </div>

    {/* Additional info sections */}
    <div className="space-y-3">
      <div className="h-3 bg-gray_5 rounded w-2/3"></div>
      <div className="h-3 bg-gray_5 rounded w-1/2"></div>
    </div>
  </div>
);

export default JobItemSkeleton;
